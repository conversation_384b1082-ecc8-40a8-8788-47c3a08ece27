from odoo import models

class Res<PERSON>artner(models.Model):
    _inherit = 'res.partner'

    def action_open_ar_ledger(self):
        action = self.env.ref('account_reports.action_account_report_partner_ledger').read()[0]
        action['context'] = {
            'active_id': self.id,
            'active_ids': self.ids,
            'active_model': 'res.partner',
            'default_partner_id': self.id,
        }
        return action 